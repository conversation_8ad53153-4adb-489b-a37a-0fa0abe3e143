
<!-- open imu and rviz -->
<launch>

    <!-- imu type, default normal -->
    <arg name="type" default="normal" doc="type [normal, modbus]"/>

    <!-- imu python -->
    <node pkg="wit_ros_imu" type="wit_$(arg type)_ros.py" name="imu" output="screen">
        <param name="port"               type = "str"    value="/dev/imu_usb"/>
        <param name="baud"               type = "int"    value="9600"/>
	<remap from="/wit/imu" to="/imu/data"/>
    </node>

    <!-- load rviz -->
    <node name="rviz" pkg="rviz" type="rviz" args="-d $(find wit_ros_imu)/rviz/wit_ros_imu.rviz">
    </node>

</launch>   
