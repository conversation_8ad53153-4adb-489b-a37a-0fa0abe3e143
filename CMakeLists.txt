# 设置一个较新的CMake版本
cmake_minimum_required(VERSION 3.0.2)

# 项目名称
project(wit_ros_imu)

# 只查找C++节点所需的组件，移除了rospy
find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  serial
)

# catkin_package() 是catkin的核心命令，它必须存在
# 它会收集包的信息，并为devel空间做准备
catkin_package()

# 添加头文件目录
# ${catkin_INCLUDE_DIRS} 会包含roscpp, std_msgs, serial的头文件路径
include_directories(
  ${catkin_INCLUDE_DIRS}
)

# 声明要编译的C++可执行文件
add_executable(GetPitch src/GetPitch.cpp)

# 将可执行文件链接到它所依赖的库
# ${catkin_LIBRARIES} 包含了roscpp, std_msgs, serial的库文件
target_link_libraries(GetPitch
  ${catkin_LIBRARIES}
)

# (可选但推荐) 添加正确的安装规则
# 这会让 `catkin_make install` 命令能够正确安装你的节点
install(TARGETS GetPitch
  DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)```
