#include <ros/ros.h>
#include <serial/serial.h>
#include <std_msgs/Float64.h>
#include <iostream>
#include <vector>
#include <numeric>
#include <cmath>

// 全局变量，以模仿Python脚本的风格
uint8_t g_buff[11];
int g_key = 0;

/**
 * @brief 计算校验和并与给定值比较
 * @param list_data 数据数组指针
 * @param len 要计算的数据长度
 * @param check_data 用于比较的校验和
 * @return bool 校验是否成功
 */
bool checkSum(const uint8_t* list_data, size_t len, uint8_t check_data) {
    uint16_t sum = 0;
    for (size_t i = 0; i < len; ++i) {
        sum += list_data[i];
    }
    return (sum & 0xff) == check_data;
}

/**
 * @brief 解析串口数据
 * @param raw_data 从串口读取的单个字节
 * @param pitch_pub ROS发布器，用于发布俯仰角
 */
void handleSerialData(uint8_t raw_data, const ros::Publisher& pitch_pub) {
    g_buff[g_key] = raw_data;
    g_key++;

    // 检查帧头
    if (g_buff[0] != 0x55) {
        g_key = 0;
        return;
    }

    // 如果数据包未满11字节，则继续接收
    if (g_key < 11) {
        return;
    }

    // 数据包接收完整
    if (g_buff[1] == 0x53) { // 角度数据
        if (checkSum(g_buff, 10, g_buff[10])) {
            // 解包数据 (小端模式，有符号short)
            int16_t roll_raw = (static_cast<int16_t>(g_buff[3]) << 8) | g_buff[2];
            int16_t pitch_raw = (static_cast<int16_t>(g_buff[5]) << 8) | g_buff[4];
            int16_t yaw_raw = (static_cast<int16_t>(g_buff[7]) << 8) | g_buff[6];

            // 转换为角度值
            double angle_degree[3];
            angle_degree[0] = static_cast<double>(roll_raw) / 32768.0 * 180.0;
            angle_degree[1] = static_cast<double>(pitch_raw) / 32768.0 * 180.0;
            angle_degree[2] = static_cast<double>(yaw_raw) / 32768.0 * 180.0;

            // 转换为弧度值
            double angle_radian[3];
            angle_radian[0] = angle_degree[0] * M_PI / 180.0;
            angle_radian[1] = angle_degree[1] * M_PI / 180.0;
            angle_radian[2] = angle_degree[2] * M_PI / 180.0;
            
            double pitch = angle_radian[1];
            
            // 创建并发布消息
            std_msgs::Float64 msg;
            msg.data = pitch;
            pitch_pub.publish(msg);
        } else {
            ROS_WARN("0x53 Checksum failure");
        }
    }

    // 为下一条消息重置缓冲区索引
    g_key = 0;
}

int main(int argc, char** argv) {
    // ROS节点初始化
    ros::init(argc, argv, "imu");
    ros::NodeHandle nh;
    
    // 从参数服务器获取参数
    std::string port;
    int baudrate;
    nh.param<std::string>("~port", port, "/dev/imu_usb");
    nh.param<int>("~baud", baudrate, 9600);
    
    ROS_INFO("IMU Type: Normal Port:%s baud:%d", port.c_str(), baudrate);

    // 设置并打开串口
    serial::Serial ser;
    try {
        ser.setPort(port);
        ser.setBaudrate(baudrate);
        serial::Timeout to = serial::Timeout::simpleTimeout(500); // 超时设置为0.5秒
        ser.setTimeout(to);
        ser.open();
    } catch (serial::IOException& e) {
        ROS_ERROR_STREAM("\033[31mSerial port opening failure: " << e.what() << "\033[0m");
        return -1;
    }

    if (ser.isOpen()) {
        ROS_INFO_STREAM("\033[32mSerial port opened successfully...\033[0m");
    } else {
        return -1;
    }

    // 创建ROS发布器
    ros::Publisher pitch_pub = nh.advertise<std_msgs::Float64>("/Ncurrent_angle", 10);
    
    // 主循环
    while (ros::ok()) {
        try {
            // 如果串口缓冲区有数据
            if (ser.available()) {
                std::vector<uint8_t> buffer;
                // 读取所有可用数据
                size_t bytes_read = ser.read(buffer, ser.available());
                // 逐字节处理
                for (size_t i = 0; i < bytes_read; ++i) {
                    handleSerialData(buffer[i], pitch_pub);
                }
            }
        } catch (std::exception &e) {
            ROS_ERROR_STREAM("Exception: " << e.what());
            ROS_ERROR_STREAM("imu disconnect");
            return -1;
        }
        ros::spinOnce(); // 处理ROS回调
    }
    
    return 0;
}